<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>top-level-items</key>
	<array>
		<dict>
			<key>destination</key>
			<dict>
				<key>location-parameters</key>
				<dict>
					<key>EndingColumnNumber</key>
					<string>4</string>
					<key>EndingLineNumber</key>
					<string>66</string>
					<key>StartingColumnNumber</key>
					<string>0</string>
					<key>StartingLineNumber</key>
					<string>66</string>
					<key>Timestamp</key>
					<string>768560664.4771791</string>
				</dict>
				<key>rebasable-url</key>
				<dict>
					<key>base</key>
					<string>workspace</string>
					<key>payload</key>
					<dict>
						<key>relative-path</key>
						<string>ios/PoseLandmarker/ViewContoller/RootViewController.swift</string>
					</dict>
				</dict>
			</dict>
			<key>text-context</key>
			<dict>
				<key>focused</key>
				<string>    </string>
				<key>leading</key>
				<string>    super.viewDidLoad()
    // Create pose landmarker helper
</string>
				<key>trailing</key>
				<string>
    inferenceViewController?.isUIEnabled = true
    runningModeTabbar.selectedItem = runningModeTabbar.items?.first
</string>
			</dict>
			<key>type</key>
			<string>bookmark</string>
		</dict>
		<dict>
			<key>destination</key>
			<dict>
				<key>location-parameters</key>
				<dict>
					<key>EndingColumnNumber</key>
					<string>6</string>
					<key>EndingLineNumber</key>
					<string>199</string>
					<key>StartingColumnNumber</key>
					<string>0</string>
					<key>StartingLineNumber</key>
					<string>199</string>
					<key>Timestamp</key>
					<string>768972732.302755</string>
				</dict>
				<key>rebasable-url</key>
				<dict>
					<key>base</key>
					<string>workspace</string>
					<key>payload</key>
					<dict>
						<key>relative-path</key>
						<string>ios/PoseLandmarker/Services/CameraFeedService.swift</string>
					</dict>
				</dict>
			</dict>
			<key>text-context</key>
			<dict>
				<key>focused</key>
				<string>      </string>
				<key>leading</key>
				<string>      self.cameraConfigurationStatus = .success
    case .notDetermined:
</string>
				<key>trailing</key>
				<string>self.sessionQueue.suspend()
      self.requestCameraAccess(completion: { (granted) in
        self.sessionQueue.resume()
</string>
			</dict>
			<key>type</key>
			<string>bookmark</string>
		</dict>
	</array>
</dict>
</plist>
