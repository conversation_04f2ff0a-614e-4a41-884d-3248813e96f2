---
description: 
globs: 
alwaysApply: true
---
1.这是一个 IOS 工程
2.使用 cocopod管理依赖
3.使用 swiftui作为视图框架
4.使用主要开发语言是 swift
5.编译项目使用`xcodebuild -workspace FitCount.xcworkspace -scheme FitCount -destination 'platform=iOS Simulator,name=iPhone 16' clean build`进行编译.
6.你需要调用编译命令检查是否有错误,如果有请修复.
7.编写swiftUI 的时候你需要完善对应的PreviewProvider
8.所有文案都需要定义在IOS 多语言规范文件下.
9.新增的代码需要丰富的注释
10.你总是说中文
11.引用字符串请使用比较新的 API 比如LocalizedStringKey
# 行动定义
当我说编译,编译项目时是指要求通过 `xcodebuild`编译项目,编译发现错误你需要修复后重新编译.
