---
description: 
globs: 
alwaysApply: false
---
编译使用xcodebuild命令行
1. 我想实现通过摄像头检测人体姿势,统计仰卧起坐的数量
2. 回调姿势的体位坐标信息回调在poseLandmarkerService函数的ResultBundle的poseLandmarkerResults
3. 后面是返回的坐标索引
    0 - nose
    1 - left eye (inner)
    2 - left eye
    3 - left eye (outer)
    4 - right eye (inner)
    5 - right eye
    6 - right eye (outer)
    7 - left ear
    8 - right ear
    9 - mouth (left)
    10 - mouth (right)
    11 - left shoulder
    12 - right shoulder
    13 - left elbow
    14 - right elbow
    15 - left wrist
    16 - right wrist
    17 - left pinky
    18 - right pinky
    19 - left index
    20 - right index
    21 - left thumb
    22 - right thumb
    23 - left hip
    24 - right hip
    25 - left knee
    26 - right knee
    27 - left ankle
    28 - right ankle
    29 - left heel
    30 - right heel
    31 - left foot index
    32 - right foot index