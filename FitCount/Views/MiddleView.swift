import SwiftUI

struct MiddleView: View {
    var body: some View {
        NavigationView {
            VStack {
                // 修复: 使用正确的本地化字符串键值引用方式
                Text(LocalizedStringKey(Tab.middle.titleKey))
                    .font(.largeTitle)
                Text(LocalizedStringKey("view.middle.content"))
                    .padding()
                Spacer()
            }
            // 修复: 使用正确的本地化字符串键值引用方式
            .navigationTitle(LocalizedStringKey(Tab.middle.titleKey))
            .navigationBarTitleDisplayMode(.inline)
        }
        .navigationViewStyle(.stack)
    }
}

// Xcode 预览
struct MiddleView_Previews: PreviewProvider {
    static var previews: some View {
        MiddleView()
            .environment(\.locale, .init(identifier: "en"))
        MiddleView()
            .environment(\.locale, .init(identifier: "zh-Hans"))
    }
} 