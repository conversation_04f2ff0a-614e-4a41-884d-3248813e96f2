import SwiftUI

struct LastView: View {
    var body: some View {
        NavigationView {
            VStack {
                // 修复: 使用正确的本地化字符串键值引用方式
                Text(LocalizedStringKey(Tab.last.titleKey))
                    .font(.largeTitle)
                Text(LocalizedStringKey("view.last.content"))
                    .padding()
                Spacer()
            }
            // 修复: 使用正确的本地化字符串键值引用方式
            .navigationTitle(LocalizedStringKey(Tab.last.titleKey))
            .navigationBarTitleDisplayMode(.inline)
        }
        .navigationViewStyle(.stack)
    }
}

// Xcode 预览
struct LastView_Previews: PreviewProvider {
    static var previews: some View {
        LastView()
            .environment(\.locale, .init(identifier: "en"))
        LastView()
            .environment(\.locale, .init(identifier: "zh-Hans"))
    }
} 