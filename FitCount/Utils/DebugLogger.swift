import Foundation

/// DebugLogger - 调试日志工具类
/// 用于在应用程序中记录不同级别的日志信息，便于开发和调试
class DebugLogger {
    /// 是否启用日志功能
    static var isEnabled = true
    
    /// 当前日志级别，只有小于或等于此级别的日志会被记录
    static var logLevel: LogLevel = .debug
    
    /// 日志级别枚举，按照严重程度从高到低排序
    enum LogLevel: Int {
        case error = 0   // 错误：表示发生了严重问题
        case warning = 1 // 警告：表示潜在问题
        case info = 2    // 信息：表示重要但非错误信息
        case debug = 3   // 调试：用于调试目的的详细信息
        case verbose = 4 // 详细：最详细的日志信息
    }
    
    /// 主要日志记录方法
    /// - Parameters:
    ///   - message: 要记录的消息内容
    ///   - level: 日志级别，默认为info
    ///   - file: 发起日志记录的文件（自动获取）
    ///   - function: 发起日志记录的函数名（自动获取）
    ///   - line: 发起日志记录的代码行号（自动获取）
    static func log(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        if isEnabled && level.rawValue <= logLevel.rawValue {
            let fileName = URL(fileURLWithPath: file).lastPathComponent
            let prefix: String
            switch level {
            case .error: prefix = "❌ ERROR"
            case .warning: prefix = "⚠️ WARNING"
            case .info: prefix = "ℹ️ INFO"
            case .debug: prefix = "🔍 DEBUG"
            case .verbose: prefix = "📝 VERBOSE"
            }
            print("\(prefix) [\(fileName):\(line)] \(function): \(message)")
        }
    }
    
    /// 记录错误级别日志的便捷方法
    static func error(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .error, file: file, function: function, line: line)
    }
    
    /// 记录警告级别日志的便捷方法
    static func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .warning, file: file, function: function, line: line)
    }
    
    /// 记录信息级别日志的便捷方法
    static func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .info, file: file, function: function, line: line)
    }
    
    /// 记录调试级别日志的便捷方法
    static func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .debug, file: file, function: function, line: line)
    }
    
    /// 记录详细级别日志的便捷方法
    static func verbose(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .verbose, file: file, function: function, line: line)
    }
}
