import SwiftUI

enum Tab: Int, Identifiable, CaseIterable, Hashable {
    case first, middle, last

    var id: Int { self.rawValue }

    var iconName: String {
        switch self {
        case .first:
            return "rectangle.portrait.on.rectangle.portrait.angled"
        case .middle:
            return "hexagon.fill" // 类似草图中的多边形
        case .last:
            return "person.crop.rectangle.stack"
        }
    }

    var title: String {
        switch self {
        case .first:
            return "tab.first.title"
        case .middle:
            return "tab.middle.title"
        case .last:
            return "tab.last.title"
        }
    }

    var titleKey: String {
        switch self {
        case .first:
            return "tab.first.title"
        case .middle:
            return "tab.middle.title"
        case .last:
            return "tab.last.title"
        }
    }
} 