// Copyright 2023 The MediaPipe Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import AVFoundation
import MediaPipeTasksVision
import UIKit

/**
 * The view controller is responsible for performing detection on incoming frames from the live camera and presenting the frames with the
 * landmark of the landmarked poses to the user.
 */
class CameraViewController: UIViewController {
  private struct Constants {
    static let edgeOffset: CGFloat = 2.0
  }
  
  weak var inferenceResultDeliveryDelegate: InferenceResultDeliveryDelegate?
  weak var interfaceUpdatesDelegate: InterfaceUpdatesDelegate?

  @IBOutlet weak var previewView: UIView!
  @IBOutlet weak var cameraUnavailableLabel: UILabel!
  @IBOutlet weak var resumeButton: UIButton!
  @IBOutlet weak var overlayView: OverlayView!
  
  // 仰卧起坐计数标签
  private var situpCountLabel: UILabel!
  
  // 角度显示标签
  private var angleLabel: UILabel!
  
  private var isSessionRunning = false
  private var isObserving = false
  private let backgroundQueue = DispatchQueue(label: "com.google.mediapipe.cameraController.backgroundQueue")
  
  // 仰卧起坐计数状态变量
  private var situpCount = 0
  private var isSitupUp = false
  private var lastHipAngle: Double = 0
  private var situpThreshold: Double = 130.0 // 仰卧起坐角度阈值
  
  // MARK: Controllers that manage functionality
  // Handles all the camera related functionality
  private lazy var cameraFeedService = CameraFeedService(previewView: previewView)
  
  private let poseLandmarkerServiceQueue = DispatchQueue(
    label: "com.google.mediapipe.cameraController.poseLandmarkerServiceQueue",
    attributes: .concurrent)
  
  // Queuing reads and writes to poseLandmarkerService using the Apple recommended way
  // as they can be read and written from multiple threads and can result in race conditions.
  private var _poseLandmarkerService: PoseLandmarkerService?
  private var poseLandmarkerService: PoseLandmarkerService? {
    get {
      poseLandmarkerServiceQueue.sync {
        return self._poseLandmarkerService
      }
    }
    set {
      poseLandmarkerServiceQueue.async(flags: .barrier) {
        self._poseLandmarkerService = newValue
      }
    }
  }

#if !targetEnvironment(simulator)
  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    initializePoseLandmarkerServiceOnSessionResumption()
    cameraFeedService.startLiveCameraSession {[weak self] cameraConfiguration in
      DispatchQueue.main.async {
        switch cameraConfiguration {
        case .failed:
          self?.presentVideoConfigurationErrorAlert()
        case .permissionDenied:
          self?.presentCameraPermissionsDeniedAlert()
        default:
          break
        }
      }
    }
  }
  
  override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
    cameraFeedService.stopSession()
    clearPoseLandmarkerServiceOnSessionInterruption()
  }
  
  override func viewDidLoad() {
    super.viewDidLoad()
    cameraFeedService.delegate = self
    // 设置仰卧起坐计数标签
    setupSitupCountLabel()
    // 设置角度显示标签
    setupAngleLabel()
  }
  
  // 设置仰卧起坐计数标签
  private func setupSitupCountLabel() {
    situpCountLabel = UILabel()
    situpCountLabel.translatesAutoresizingMaskIntoConstraints = false
    situpCountLabel.text = "仰卧起坐次数: 0"
    situpCountLabel.textColor = .white
    situpCountLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
    situpCountLabel.textAlignment = .center
    situpCountLabel.layer.cornerRadius = 10
    situpCountLabel.clipsToBounds = true
    situpCountLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
    
    view.addSubview(situpCountLabel)
    
    NSLayoutConstraint.activate([
      situpCountLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
      situpCountLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
      situpCountLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
      situpCountLabel.heightAnchor.constraint(equalToConstant: 44)
    ])
  }
  
  // 设置角度显示标签
  private func setupAngleLabel() {
    angleLabel = UILabel()
    angleLabel.translatesAutoresizingMaskIntoConstraints = false
    angleLabel.text = "当前角度: 0.0°"
    angleLabel.textColor = .white
    angleLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
    angleLabel.textAlignment = .center
    angleLabel.layer.cornerRadius = 10
    angleLabel.clipsToBounds = true
    angleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
    
    view.addSubview(angleLabel)
    
    NSLayoutConstraint.activate([
      angleLabel.topAnchor.constraint(equalTo: situpCountLabel.bottomAnchor, constant: 10),
      angleLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
      angleLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
      angleLabel.heightAnchor.constraint(equalToConstant: 44)
    ])
  }
  
  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    cameraFeedService.updateVideoPreviewLayer(toFrame: previewView.bounds)
  }
  
  override func viewWillLayoutSubviews() {
    super.viewWillLayoutSubviews()
    cameraFeedService.updateVideoPreviewLayer(toFrame: previewView.bounds)
  }
#endif
  
  // Resume camera session when click button resume
  @IBAction func onClickResume(_ sender: Any) {
    cameraFeedService.resumeInterruptedSession {[weak self] isSessionRunning in
      if isSessionRunning {
        self?.resumeButton.isHidden = true
        self?.cameraUnavailableLabel.isHidden = true
        self?.initializePoseLandmarkerServiceOnSessionResumption()
      }
    }
  }
  
  private func presentCameraPermissionsDeniedAlert() {
    let alertController = UIAlertController(
      title: "Camera Permissions Denied",
      message:
        "Camera permissions have been denied for this app. You can change this by going to Settings",
      preferredStyle: .alert)
    
    let cancelAction = UIAlertAction(title: "Cancel", style: .cancel, handler: nil)
    let settingsAction = UIAlertAction(title: "Settings", style: .default) { (action) in
      UIApplication.shared.open(
        URL(string: UIApplication.openSettingsURLString)!, options: [:], completionHandler: nil)
    }
    alertController.addAction(cancelAction)
    alertController.addAction(settingsAction)
    
    present(alertController, animated: true, completion: nil)
  }
  
  private func presentVideoConfigurationErrorAlert() {
    let alert = UIAlertController(
      title: "Camera Configuration Failed",
      message: "There was an error while configuring camera.",
      preferredStyle: .alert)
    alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))
    
    self.present(alert, animated: true)
  }
  
  private func initializePoseLandmarkerServiceOnSessionResumption() {
    clearAndInitializePoseLandmarkerService()
    startObserveConfigChanges()
  }
  
  @objc private func clearAndInitializePoseLandmarkerService() {
    poseLandmarkerService = nil
    poseLandmarkerService = PoseLandmarkerService
      .liveStreamPoseLandmarkerService(
        modelPath: InferenceConfigurationManager.sharedInstance.model.modelPath,
        numPoses: InferenceConfigurationManager.sharedInstance.numPoses,
        minPoseDetectionConfidence: InferenceConfigurationManager.sharedInstance.minPoseDetectionConfidence,
        minPosePresenceConfidence: InferenceConfigurationManager.sharedInstance.minPosePresenceConfidence,
        minTrackingConfidence: InferenceConfigurationManager.sharedInstance.minTrackingConfidence,
        liveStreamDelegate: self,
        delegate: InferenceConfigurationManager.sharedInstance.delegate)
  }
  
  private func clearPoseLandmarkerServiceOnSessionInterruption() {
    stopObserveConfigChanges()
    poseLandmarkerService = nil
  }
  
  private func startObserveConfigChanges() {
    NotificationCenter.default
      .addObserver(self,
                   selector: #selector(clearAndInitializePoseLandmarkerService),
                   name: InferenceConfigurationManager.notificationName,
                   object: nil)
    isObserving = true
  }
  
  private func stopObserveConfigChanges() {
    if isObserving {
      NotificationCenter.default
        .removeObserver(self,
                        name:InferenceConfigurationManager.notificationName,
                        object: nil)
    }
    isObserving = false
  }
}

extension CameraViewController: CameraFeedServiceDelegate {
  
  func didOutput(sampleBuffer: CMSampleBuffer, orientation: UIImage.Orientation) {
    let currentTimeMs = Date().timeIntervalSince1970 * 1000
    // Pass the pixel buffer to mediapipe
    backgroundQueue.async { [weak self] in
      self?.poseLandmarkerService?.detectAsync(
        sampleBuffer: sampleBuffer,
        orientation: orientation,
        timeStamps: Int(currentTimeMs))
    }
  }
  
  // MARK: Session Handling Alerts
  func sessionWasInterrupted(canResumeManually resumeManually: Bool) {
    // Updates the UI when session is interupted.
    if resumeManually {
      resumeButton.isHidden = false
    } else {
      cameraUnavailableLabel.isHidden = false
    }
    clearPoseLandmarkerServiceOnSessionInterruption()
  }
  
  func sessionInterruptionEnded() {
    // Updates UI once session interruption has ended.
    cameraUnavailableLabel.isHidden = true
    resumeButton.isHidden = true
    initializePoseLandmarkerServiceOnSessionResumption()
  }
  
  func didEncounterSessionRuntimeError() {
    // Handles session run time error by updating the UI and providing a button if session can be
    // manually resumed.
    resumeButton.isHidden = false
    clearPoseLandmarkerServiceOnSessionInterruption()
  }
}

// MARK: PoseLandmarkerServiceLiveStreamDelegate
extension CameraViewController: PoseLandmarkerServiceLiveStreamDelegate {

  func poseLandmarkerService(
    _ poseLandmarkerService: PoseLandmarkerService,
    didFinishDetection result: ResultBundle?,
    error: Error?) {
      DispatchQueue.main.async { [weak self] in
        guard let weakSelf = self else { return }
        weakSelf.inferenceResultDeliveryDelegate?.didPerformInference(result: result)
        guard let poseLandmarkerResult = result?.poseLandmarkerResults.first as? PoseLandmarkerResult else { return }
        
        // 计算仰卧起坐姿势
        if !poseLandmarkerResult.landmarks.isEmpty {
          let landmarks = poseLandmarkerResult.landmarks[0]
          
          // 确保有足够的关键点进行仰卧起坐检测
          if landmarks.count >= 33 {
            // 使用肩膀(11)、臀部(23)和膝盖(25)关键点计算角度
            let shoulder = landmarks[11]
            let hip = landmarks[23]
            let knee = landmarks[25]
            
            // 计算角度 - 使用世界坐标系的点来计算，这样就不受屏幕旋转影响
            var angle: Double = 0.0
            
            // 检查是否有世界坐标数据
            if !poseLandmarkerResult.worldLandmarks.isEmpty && poseLandmarkerResult.worldLandmarks[0].count >= 33 {
              let worldLandmarks = poseLandmarkerResult.worldLandmarks[0]
              let worldShoulder = worldLandmarks[11]
              let worldHip = worldLandmarks[23]
              let worldKnee = worldLandmarks[25]
              
              // 使用世界坐标计算角度
              let radians: Double = atan2(Double(worldKnee.y - worldHip.y), Double(worldKnee.x - worldHip.x)) - 
                                    atan2(Double(worldShoulder.y - worldHip.y), Double(worldShoulder.x - worldHip.x))
              angle = abs(radians * 180.0 / .pi)
              
              if angle > 180.0 {
                angle = 360.0 - angle
              }
            } else {
              // 如果没有世界坐标，则使用图像坐标
              let radians: Double = atan2(Double(knee.y - hip.y), Double(knee.x - hip.x)) - 
                                    atan2(Double(shoulder.y - hip.y), Double(shoulder.x - hip.x))
              angle = abs(radians * 180.0 / .pi)
              
              if angle > 180.0 {
                angle = 360.0 - angle
              }
            }
            
            // 更新角度标签显示
            weakSelf.angleLabel.text = String(format: "当前角度: %.1f°", angle)
            
            // 更新仰卧起坐计数
            // 判断是否完成一次仰卧起坐
            if !weakSelf.isSitupUp && angle < weakSelf.situpThreshold {
                weakSelf.isSitupUp = true
            } else if weakSelf.isSitupUp && angle > weakSelf.situpThreshold {
                weakSelf.isSitupUp = false
                weakSelf.situpCount += 1
                
                // 更新UI
                weakSelf.situpCountLabel.text = "仰卧起坐次数: \(weakSelf.situpCount)"
            }
            
            weakSelf.lastHipAngle = angle
          }
        }
        
        let imageSize = weakSelf.cameraFeedService.videoResolution
        let poseOverlays = OverlayView.poseOverlays(
            fromMultiplePoseLandmarks: poseLandmarkerResult.landmarks,
          inferredOnImageOfSize: imageSize,
          ovelayViewSize: weakSelf.overlayView.bounds.size,
          imageContentMode: weakSelf.overlayView.imageContentMode,
          andOrientation: UIImage.Orientation.from(
            deviceOrientation: UIDevice.current.orientation))
        weakSelf.overlayView.draw(poseOverlays: poseOverlays,
                         inBoundsOfContentImageOfSize: imageSize,
                         imageContentMode: weakSelf.cameraFeedService.videoGravity.contentMode)
      }
    }
}

// MARK: - AVLayerVideoGravity Extension
extension AVLayerVideoGravity {
  var contentMode: UIView.ContentMode {
    switch self {
    case .resizeAspectFill:
      return .scaleAspectFill
    case .resizeAspect:
      return .scaleAspectFit
    case .resize:
      return .scaleToFill
    default:
      return .scaleAspectFill
    }
  }
}
