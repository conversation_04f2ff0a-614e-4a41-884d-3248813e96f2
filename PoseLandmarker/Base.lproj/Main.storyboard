<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="52l-qN-uG0">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="UIMenu" message="Requires Xcode 11 or later." minToolsVersion="11.0" requiredIntegratedClassName="UICommandDiff"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Root View Controller-->
        <scene sceneID="jZ4-w7-UPh">
            <objects>
                <viewController id="52l-qN-uG0" customClass="RootViewController" customModule="PoseLandmarker" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="2Qh-dN-S58">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <containerView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rjo-OY-wkN">
                                <rect key="frame" x="0.0" y="152" width="393" height="700"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </containerView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="z7m-k8-X4L">
                                <rect key="frame" x="0.0" y="59" width="107.66666666666667" height="45"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XfM-80-TTX">
                                <rect key="frame" x="285.66666666666669" y="59" width="107.33333333333331" height="44"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FVR-Cx-oSb">
                                <rect key="frame" x="107.66666666666669" y="59" width="178" height="44"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="PoseLandmarker" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="42E-Ov-MEY">
                                        <rect key="frame" x="39.999999999999986" y="11.666666666666671" width="138" height="21"/>
                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_mediapipe" translatesAutoresizingMaskIntoConstraints="NO" id="jA5-8l-gmu">
                                        <rect key="frame" x="0.0" y="2" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="3O3-ci-rV6"/>
                                            <constraint firstAttribute="width" constant="40" id="bEj-0x-Qpq"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="42E-Ov-MEY" firstAttribute="centerY" secondItem="FVR-Cx-oSb" secondAttribute="centerY" id="7Zt-kp-Wja"/>
                                    <constraint firstAttribute="height" constant="44" id="Bhj-A9-AYK"/>
                                    <constraint firstItem="42E-Ov-MEY" firstAttribute="leading" secondItem="jA5-8l-gmu" secondAttribute="trailing" id="JKh-Ah-upM"/>
                                    <constraint firstItem="jA5-8l-gmu" firstAttribute="leading" secondItem="FVR-Cx-oSb" secondAttribute="leading" id="YIV-zH-YYg"/>
                                    <constraint firstAttribute="trailing" secondItem="42E-Ov-MEY" secondAttribute="trailing" id="YcK-5V-JKb"/>
                                    <constraint firstItem="jA5-8l-gmu" firstAttribute="centerY" secondItem="FVR-Cx-oSb" secondAttribute="centerY" id="qez-bd-CvQ"/>
                                </constraints>
                            </view>
                            <tabBar contentMode="center" translatesAutoresizingMaskIntoConstraints="NO" id="wh4-sl-2fW">
                                <rect key="frame" x="0.0" y="103" width="393" height="49"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <items>
                                    <tabBarItem title="" image="ic_camera" id="cZA-BJ-w3x"/>
                                    <tabBarItem title="" image="ic_library" id="tov-i7-MfF"/>
                                </items>
                                <color key="selectedImageTintColor" red="0.070588235289999995" green="0.70980392160000005" blue="0.79607843140000001" alpha="0.84705882349999995" colorSpace="calibratedRGB"/>
                            </tabBar>
                            <view opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fjN-uX-YES">
                                <rect key="frame" x="0.0" y="566" width="393" height="400"/>
                                <subviews>
                                    <containerView opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dXK-XY-UFV">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="400"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <segue destination="zgE-Xo-ZSc" kind="embed" identifier="EMBED" id="Ne4-GD-QBm"/>
                                        </connections>
                                    </containerView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="dXK-XY-UFV" secondAttribute="bottom" id="Ioc-1B-kKZ"/>
                                    <constraint firstAttribute="height" constant="400" id="Rza-hL-hyF"/>
                                    <constraint firstAttribute="trailing" secondItem="dXK-XY-UFV" secondAttribute="trailing" id="cGZ-cS-8UG"/>
                                    <constraint firstItem="dXK-XY-UFV" firstAttribute="top" secondItem="fjN-uX-YES" secondAttribute="top" id="hMv-gb-YTQ"/>
                                    <constraint firstItem="dXK-XY-UFV" firstAttribute="leading" secondItem="fjN-uX-YES" secondAttribute="leading" id="xaG-x3-dFE"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="gWa-oL-KkV"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="z7m-k8-X4L" firstAttribute="top" secondItem="FVR-Cx-oSb" secondAttribute="top" id="13F-Ce-VZk"/>
                            <constraint firstItem="Rjo-OY-wkN" firstAttribute="top" secondItem="wh4-sl-2fW" secondAttribute="bottom" id="16h-Fa-gG7"/>
                            <constraint firstItem="gWa-oL-KkV" firstAttribute="trailing" secondItem="Rjo-OY-wkN" secondAttribute="trailing" id="A3X-Ak-kOa"/>
                            <constraint firstItem="FVR-Cx-oSb" firstAttribute="centerX" secondItem="gWa-oL-KkV" secondAttribute="centerX" id="Jgm-n8-Xca"/>
                            <constraint firstItem="wh4-sl-2fW" firstAttribute="leading" secondItem="gWa-oL-KkV" secondAttribute="leading" id="KYY-0V-co2"/>
                            <constraint firstItem="wh4-sl-2fW" firstAttribute="trailing" secondItem="gWa-oL-KkV" secondAttribute="trailing" id="LNl-yW-0vO"/>
                            <constraint firstAttribute="bottom" secondItem="fjN-uX-YES" secondAttribute="bottom" constant="-114" id="SAs-Ht-5OK"/>
                            <constraint firstItem="XfM-80-TTX" firstAttribute="top" secondItem="FVR-Cx-oSb" secondAttribute="top" id="SB8-hW-Swm"/>
                            <constraint firstItem="XfM-80-TTX" firstAttribute="bottom" secondItem="FVR-Cx-oSb" secondAttribute="bottom" id="SY1-e4-FM1"/>
                            <constraint firstItem="fjN-uX-YES" firstAttribute="leading" secondItem="2Qh-dN-S58" secondAttribute="leading" id="SsD-0M-69r"/>
                            <constraint firstItem="z7m-k8-X4L" firstAttribute="leading" secondItem="gWa-oL-KkV" secondAttribute="leading" id="W3d-bX-wxf"/>
                            <constraint firstItem="z7m-k8-X4L" firstAttribute="bottom" secondItem="FVR-Cx-oSb" secondAttribute="bottom" constant="1" id="Z3p-CM-gyb"/>
                            <constraint firstItem="FVR-Cx-oSb" firstAttribute="leading" secondItem="z7m-k8-X4L" secondAttribute="trailing" id="c3H-fv-g1W"/>
                            <constraint firstItem="wh4-sl-2fW" firstAttribute="top" secondItem="FVR-Cx-oSb" secondAttribute="bottom" id="dKO-n4-DCw"/>
                            <constraint firstItem="Rjo-OY-wkN" firstAttribute="leading" secondItem="gWa-oL-KkV" secondAttribute="leading" id="fCx-jI-QYc"/>
                            <constraint firstItem="gWa-oL-KkV" firstAttribute="trailing" secondItem="XfM-80-TTX" secondAttribute="trailing" id="iMO-d4-yWI"/>
                            <constraint firstItem="XfM-80-TTX" firstAttribute="leading" secondItem="FVR-Cx-oSb" secondAttribute="trailing" id="j7Z-XE-LE5"/>
                            <constraint firstAttribute="bottom" secondItem="Rjo-OY-wkN" secondAttribute="bottom" id="nGj-xr-T2v"/>
                            <constraint firstAttribute="trailing" secondItem="fjN-uX-YES" secondAttribute="trailing" id="nn0-fU-ykZ"/>
                            <constraint firstItem="FVR-Cx-oSb" firstAttribute="top" secondItem="gWa-oL-KkV" secondAttribute="top" id="zat-n8-cGD"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="bottomSheetViewBottomSpace" destination="SAs-Ht-5OK" id="4DD-Sk-O7R"/>
                        <outlet property="bottomViewHeightConstraint" destination="Rza-hL-hyF" id="7rU-Be-Gc6"/>
                        <outlet property="runningModeTabbar" destination="wh4-sl-2fW" id="3Ag-iu-d8M"/>
                        <outlet property="tabBarContainerView" destination="Rjo-OY-wkN" id="bWr-I6-BTo"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="4r6-Oc-kXs" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1022" y="1"/>
        </scene>
        <!--Bottom Sheet View Controller-->
        <scene sceneID="nVP-CX-rj2">
            <objects>
                <viewController id="zgE-Xo-ZSc" customClass="BottomSheetViewController" customModule="PoseLandmarker" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" clipsSubviews="YES" contentMode="scaleToFill" id="Knd-PN-meG">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="400"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AOI-Gh-SLK">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="400"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Detection Confidence" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iaZ-s2-My0">
                                        <rect key="frame" x="16" y="96" width="141" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stepper opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.10000000000000001" minimumValue="0.10000000000000001" maximumValue="0.80000000000000004" stepValue="0.10000000000000001" translatesAutoresizingMaskIntoConstraints="NO" id="0nH-Zx-p69">
                                        <rect key="frame" x="283" y="88.666666666666671" width="94" height="32"/>
                                        <color key="backgroundColor" red="0.0" green="0.49803921569999998" blue="0.5450980392" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="layer.cornerRadius" value="8"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="minPoseDetectionConfidenceStepperValueChanged:" destination="zgE-Xo-ZSc" eventType="valueChanged" id="the-t8-8nc"/>
                                        </connections>
                                    </stepper>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.5" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s05-yK-gXh">
                                        <rect key="frame" x="258.66666666666669" y="96" width="20.333333333333314" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0Hl-aV-l7w">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="41"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="41" id="oRM-Jm-qt8"/>
                                        </constraints>
                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                        <state key="normal" image="ic_expand_up">
                                            <color key="titleShadowColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                                        </state>
                                        <state key="selected" image="ic_expand_down"/>
                                        <connections>
                                            <action selector="expandButtonTouchUpInside:" destination="zgE-Xo-ZSc" eventType="touchUpInside" id="5GT-V8-7MY"/>
                                        </connections>
                                    </button>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Inference Time" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="d2C-9O-82c">
                                        <rect key="frame" x="15.999999999999993" y="53" width="96.333333333333314" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cRO-ho-FXh">
                                        <rect key="frame" x="377" y="61.666666666666664" width="0.0" height="0.0"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tZo-oH-ToE">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="1"/>
                                        <color key="backgroundColor" red="0.070588235289999995" green="0.70980392160000005" blue="0.79607843140000001" alpha="0.84705882349999995" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="1" id="Xlb-hr-NyK"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Presence Confidence" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="06h-cY-2bs">
                                        <rect key="frame" x="16" y="140" width="139" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stepper opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.10000000000000001" minimumValue="0.10000000000000001" maximumValue="0.80000000000000004" stepValue="0.10000000000000001" translatesAutoresizingMaskIntoConstraints="NO" id="1fr-hY-4II">
                                        <rect key="frame" x="283" y="132.66666666666666" width="94" height="32"/>
                                        <color key="backgroundColor" red="0.0" green="0.49803921569999998" blue="0.5450980392" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="94" id="xjC-rY-MzI"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="layer.cornerRadius" value="8"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="minPosePresenceConfidenceStepperValueChanged:" destination="zgE-Xo-ZSc" eventType="valueChanged" id="fpX-XB-V9a"/>
                                        </connections>
                                    </stepper>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.3" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="J1Y-43-5yW">
                                        <rect key="frame" x="258.33333333333331" y="140" width="20.666666666666686" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" text="Tracking Confidence" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dzb-pb-2Oi">
                                        <rect key="frame" x="16" y="184" width="134" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stepper opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.10000000000000001" minimumValue="0.10000000000000001" maximumValue="0.80000000000000004" stepValue="0.10000000000000001" translatesAutoresizingMaskIntoConstraints="NO" id="DDi-33-JAf">
                                        <rect key="frame" x="283" y="176.66666666666666" width="94" height="32"/>
                                        <color key="backgroundColor" red="0.0" green="0.49803921569999998" blue="0.5450980392" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="94" id="EiO-gX-PlY"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="layer.cornerRadius" value="8"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="minTrackingConfidenceStepperValueChanged:" destination="zgE-Xo-ZSc" eventType="valueChanged" id="TyZ-nz-Yu6"/>
                                        </connections>
                                    </stepper>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.3" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uaN-jh-dWj">
                                        <rect key="frame" x="258.33333333333331" y="184" width="20.666666666666686" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stepper opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="1" minimumValue="1" maximumValue="2" translatesAutoresizingMaskIntoConstraints="NO" id="tad-jn-O5U">
                                        <rect key="frame" x="283" y="220.66666666666666" width="94" height="32"/>
                                        <color key="backgroundColor" red="0.0" green="0.49803921569999998" blue="0.5450980392" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="layer.cornerRadius" value="8"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="numPosesStepperValueChanged:" destination="zgE-Xo-ZSc" eventType="valueChanged" id="ti0-YY-lAk"/>
                                        </connections>
                                    </stepper>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Num Poses" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jCg-YX-6ga">
                                        <rect key="frame" x="16" y="228" width="73" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ME8-eS-OTB">
                                        <rect key="frame" x="272.33333333333331" y="228" width="6.6666666666666856" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" showsMenuAsPrimaryAction="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yOt-Ia-U4X">
                                        <rect key="frame" x="353" y="264.66666666666669" width="24" height="32"/>
                                        <color key="backgroundColor" red="0.0" green="0.49803921569999998" blue="0.5450980392" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="32" id="RwE-MF-Fs3"/>
                                        </constraints>
                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <menu key="menu" id="tEQ-9f-FmW">
                                            <children>
                                                <command title="item1" id="FjR-Te-8BS"/>
                                                <command title="item2" id="auv-3x-0OE"/>
                                            </children>
                                        </menu>
                                        <buttonConfiguration key="configuration" style="plain"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="layer.cornerRadius" value="8"/>
                                        </userDefinedRuntimeAttributes>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Model" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TFK-dy-OSf">
                                        <rect key="frame" x="16" y="272" width="40" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" showsMenuAsPrimaryAction="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IE7-vD-hCX">
                                        <rect key="frame" x="353" y="308.66666666666669" width="24" height="32"/>
                                        <color key="backgroundColor" red="0.0" green="0.49803921569999998" blue="0.5450980392" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="32" id="qkM-UP-hUb"/>
                                        </constraints>
                                        <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <menu key="menu" id="5ll-2C-E0V">
                                            <children>
                                                <command title="item1" id="VOa-rD-6qY"/>
                                                <command title="item2" id="RrD-6s-Lt4"/>
                                            </children>
                                        </menu>
                                        <buttonConfiguration key="configuration" style="plain"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="layer.cornerRadius" value="8"/>
                                        </userDefinedRuntimeAttributes>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Delegate" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TDB-Q1-Qpf">
                                        <rect key="frame" x="16" y="316" width="58" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="IE7-vD-hCX" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="TDB-Q1-Qpf" secondAttribute="trailing" id="36c-Hu-GNt"/>
                                    <constraint firstItem="IE7-vD-hCX" firstAttribute="centerY" secondItem="TDB-Q1-Qpf" secondAttribute="centerY" id="39H-dv-4Fi"/>
                                    <constraint firstItem="tad-jn-O5U" firstAttribute="top" secondItem="DDi-33-JAf" secondAttribute="bottom" constant="12" id="3f3-bg-DVN"/>
                                    <constraint firstItem="ME8-eS-OTB" firstAttribute="centerY" secondItem="tad-jn-O5U" secondAttribute="centerY" id="4FL-XQ-Him"/>
                                    <constraint firstItem="1fr-hY-4II" firstAttribute="trailing" secondItem="0nH-Zx-p69" secondAttribute="trailing" id="5T6-MD-Uko"/>
                                    <constraint firstAttribute="trailing" secondItem="0nH-Zx-p69" secondAttribute="trailing" constant="16" id="5xr-fC-LfQ"/>
                                    <constraint firstItem="jCg-YX-6ga" firstAttribute="leading" secondItem="iaZ-s2-My0" secondAttribute="leading" id="6kr-Ui-TIb"/>
                                    <constraint firstItem="yOt-Ia-U4X" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="TFK-dy-OSf" secondAttribute="trailing" id="8pX-2f-JPm"/>
                                    <constraint firstItem="jCg-YX-6ga" firstAttribute="centerY" secondItem="tad-jn-O5U" secondAttribute="centerY" id="9dd-nl-UN1"/>
                                    <constraint firstItem="IE7-vD-hCX" firstAttribute="top" secondItem="yOt-Ia-U4X" secondAttribute="bottom" constant="12" id="9uE-zL-i1d"/>
                                    <constraint firstItem="0nH-Zx-p69" firstAttribute="leading" secondItem="s05-yK-gXh" secondAttribute="trailing" constant="4" id="Bda-ye-Je2"/>
                                    <constraint firstItem="yOt-Ia-U4X" firstAttribute="top" secondItem="tad-jn-O5U" secondAttribute="bottom" constant="12" id="Bnn-d5-Nhi"/>
                                    <constraint firstItem="yOt-Ia-U4X" firstAttribute="trailing" secondItem="0nH-Zx-p69" secondAttribute="trailing" id="D2z-7E-YnB"/>
                                    <constraint firstItem="J1Y-43-5yW" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="06h-cY-2bs" secondAttribute="trailing" id="GMM-8D-3Gb"/>
                                    <constraint firstItem="TFK-dy-OSf" firstAttribute="leading" secondItem="iaZ-s2-My0" secondAttribute="leading" id="Kur-VI-at9"/>
                                    <constraint firstItem="DDi-33-JAf" firstAttribute="centerY" secondItem="dzb-pb-2Oi" secondAttribute="centerY" id="LXu-LA-2Pg"/>
                                    <constraint firstItem="ME8-eS-OTB" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="jCg-YX-6ga" secondAttribute="trailing" id="LcM-fq-c27"/>
                                    <constraint firstItem="cRO-ho-FXh" firstAttribute="centerY" secondItem="d2C-9O-82c" secondAttribute="centerY" id="MKa-V2-38z"/>
                                    <constraint firstItem="0Hl-aV-l7w" firstAttribute="leading" secondItem="AOI-Gh-SLK" secondAttribute="leading" id="OQf-RZ-3g1"/>
                                    <constraint firstItem="DDi-33-JAf" firstAttribute="leading" secondItem="uaN-jh-dWj" secondAttribute="trailing" constant="4" id="PWf-dd-rcZ"/>
                                    <constraint firstItem="0nH-Zx-p69" firstAttribute="trailing" secondItem="cRO-ho-FXh" secondAttribute="trailing" id="S6M-bn-pkr"/>
                                    <constraint firstItem="tZo-oH-ToE" firstAttribute="leading" secondItem="AOI-Gh-SLK" secondAttribute="leading" id="SpH-HP-7sh"/>
                                    <constraint firstItem="IE7-vD-hCX" firstAttribute="trailing" secondItem="0nH-Zx-p69" secondAttribute="trailing" id="VWS-Op-brO"/>
                                    <constraint firstItem="06h-cY-2bs" firstAttribute="leading" secondItem="iaZ-s2-My0" secondAttribute="leading" id="Y0z-QL-24a"/>
                                    <constraint firstItem="DDi-33-JAf" firstAttribute="top" secondItem="1fr-hY-4II" secondAttribute="bottom" constant="12" id="Yc1-tj-mGp"/>
                                    <constraint firstItem="J1Y-43-5yW" firstAttribute="centerY" secondItem="1fr-hY-4II" secondAttribute="centerY" id="Yox-g2-8gj"/>
                                    <constraint firstItem="iaZ-s2-My0" firstAttribute="leading" secondItem="AOI-Gh-SLK" secondAttribute="leading" constant="16" id="a2w-hd-0Jy"/>
                                    <constraint firstItem="cRO-ho-FXh" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="d2C-9O-82c" secondAttribute="trailing" id="aYp-dR-YLG"/>
                                    <constraint firstItem="s05-yK-gXh" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iaZ-s2-My0" secondAttribute="trailing" id="axL-QV-mDy"/>
                                    <constraint firstItem="iaZ-s2-My0" firstAttribute="leading" secondItem="d2C-9O-82c" secondAttribute="leading" id="b2T-co-cnQ"/>
                                    <constraint firstItem="uaN-jh-dWj" firstAttribute="centerY" secondItem="DDi-33-JAf" secondAttribute="centerY" id="ba6-Wn-7xR"/>
                                    <constraint firstItem="DDi-33-JAf" firstAttribute="trailing" secondItem="0nH-Zx-p69" secondAttribute="trailing" id="bxj-V0-YXj"/>
                                    <constraint firstItem="s05-yK-gXh" firstAttribute="centerY" secondItem="0nH-Zx-p69" secondAttribute="centerY" id="i6B-0T-nRV"/>
                                    <constraint firstItem="uaN-jh-dWj" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="dzb-pb-2Oi" secondAttribute="trailing" id="jih-9M-0it"/>
                                    <constraint firstItem="1fr-hY-4II" firstAttribute="top" secondItem="0nH-Zx-p69" secondAttribute="bottom" constant="12" id="krl-wP-aEb"/>
                                    <constraint firstItem="IE7-vD-hCX" firstAttribute="trailing" secondItem="0nH-Zx-p69" secondAttribute="trailing" id="m6a-gA-1E3"/>
                                    <constraint firstItem="tad-jn-O5U" firstAttribute="trailing" secondItem="0nH-Zx-p69" secondAttribute="trailing" id="mJY-nJ-FBy"/>
                                    <constraint firstItem="1fr-hY-4II" firstAttribute="leading" secondItem="J1Y-43-5yW" secondAttribute="trailing" constant="4" id="mLH-o1-c1c"/>
                                    <constraint firstItem="d2C-9O-82c" firstAttribute="top" secondItem="0Hl-aV-l7w" secondAttribute="bottom" constant="12" id="mcI-24-pk6"/>
                                    <constraint firstItem="iaZ-s2-My0" firstAttribute="centerY" secondItem="0nH-Zx-p69" secondAttribute="centerY" id="o81-hG-imF"/>
                                    <constraint firstItem="yOt-Ia-U4X" firstAttribute="centerY" secondItem="TFK-dy-OSf" secondAttribute="centerY" id="p2r-Yh-j9l"/>
                                    <constraint firstItem="iaZ-s2-My0" firstAttribute="top" secondItem="d2C-9O-82c" secondAttribute="bottom" constant="26" id="pPe-bR-0qO"/>
                                    <constraint firstItem="0Hl-aV-l7w" firstAttribute="top" secondItem="AOI-Gh-SLK" secondAttribute="top" id="rl8-eu-Q06"/>
                                    <constraint firstItem="TDB-Q1-Qpf" firstAttribute="leading" secondItem="iaZ-s2-My0" secondAttribute="leading" id="tXX-fA-BuC"/>
                                    <constraint firstItem="tad-jn-O5U" firstAttribute="leading" secondItem="ME8-eS-OTB" secondAttribute="trailing" constant="4" id="txK-t9-38i"/>
                                    <constraint firstItem="dzb-pb-2Oi" firstAttribute="leading" secondItem="iaZ-s2-My0" secondAttribute="leading" id="uPl-xe-HGE"/>
                                    <constraint firstItem="1fr-hY-4II" firstAttribute="centerY" secondItem="06h-cY-2bs" secondAttribute="centerY" id="whL-o0-qmm"/>
                                    <constraint firstAttribute="trailing" secondItem="tZo-oH-ToE" secondAttribute="trailing" id="xSe-Ss-8WB"/>
                                    <constraint firstItem="tZo-oH-ToE" firstAttribute="top" secondItem="AOI-Gh-SLK" secondAttribute="top" id="yoR-Hn-w9J"/>
                                    <constraint firstAttribute="trailing" secondItem="0Hl-aV-l7w" secondAttribute="trailing" id="zlY-Lj-dQD"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="2Cd-sg-sA6"/>
                        <color key="backgroundColor" white="1" alpha="0.79745960888843537" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="AOI-Gh-SLK" firstAttribute="leading" secondItem="Knd-PN-meG" secondAttribute="leading" id="7P2-zE-HaO"/>
                            <constraint firstItem="AOI-Gh-SLK" firstAttribute="top" secondItem="Knd-PN-meG" secondAttribute="top" id="N8h-sR-kbF"/>
                            <constraint firstItem="AOI-Gh-SLK" firstAttribute="bottom" secondItem="Knd-PN-meG" secondAttribute="bottom" id="QxH-QX-RBD"/>
                            <constraint firstItem="AOI-Gh-SLK" firstAttribute="trailing" secondItem="Knd-PN-meG" secondAttribute="trailing" id="n8N-io-mxd"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="BIS-1q-gQH"/>
                    <connections>
                        <outlet property="chooseDelegateButton" destination="IE7-vD-hCX" id="lLK-gk-Qhl"/>
                        <outlet property="chooseModelButton" destination="yOt-Ia-U4X" id="53S-Qz-XRG"/>
                        <outlet property="inferenceTimeLabel" destination="cRO-ho-FXh" id="E3e-km-74r"/>
                        <outlet property="inferenceTimeNameLabel" destination="d2C-9O-82c" id="O7p-Pr-lsE"/>
                        <outlet property="minPoseDetectionConfidenceStepper" destination="0nH-Zx-p69" id="UPk-Tc-SO0"/>
                        <outlet property="minPoseDetectionConfidenceValueLabel" destination="s05-yK-gXh" id="5Va-hn-CuZ"/>
                        <outlet property="minPosePresenceConfidenceStepper" destination="1fr-hY-4II" id="8pT-6m-0gG"/>
                        <outlet property="minPosePresenceConfidenceValueLabel" destination="J1Y-43-5yW" id="MMb-wQ-EMs"/>
                        <outlet property="minTrackingConfidenceStepper" destination="DDi-33-JAf" id="rfb-jb-DTW"/>
                        <outlet property="minTrackingConfidenceValueLabel" destination="uaN-jh-dWj" id="Y62-Ep-9un"/>
                        <outlet property="numPosesStepper" destination="tad-jn-O5U" id="K5T-5S-0BS"/>
                        <outlet property="numPosesValueLabel" destination="ME8-eS-OTB" id="ew6-qT-hAS"/>
                        <outlet property="toggleBottomSheetButton" destination="0Hl-aV-l7w" id="W14-lW-Pf8"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="7XD-JF-Vy4" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-143" y="127"/>
        </scene>
        <!--Camera View Controller-->
        <scene sceneID="myg-br-fzT">
            <objects>
                <viewController storyboardIdentifier="CAMERA_VIEW_CONTROLLER" id="myJ-al-UoI" customClass="CameraViewController" customModule="PoseLandmarker" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="LR5-NX-OG0">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Camera unavailable" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vtt-MU-A4x">
                                <rect key="frame" x="10" y="79" width="373" height="20"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xgW-WZ-vbo" userLabel="Resume">
                                <rect key="frame" x="158" y="408.66666666666669" width="77" height="35"/>
                                <color key="backgroundColor" red="0.0" green="0.49803921569999998" blue="0.5450980392" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <color key="tintColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="Resum"/>
                                <connections>
                                    <action selector="onClickResume:" destination="myJ-al-UoI" eventType="touchUpInside" id="QSK-lp-FXg"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uBH-jh-cod" userLabel="PreviewView">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="m4r-LP-sGS" customClass="OverlayView" customModule="PoseLandmarker">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bEV-so-nno"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="m4r-LP-sGS" secondAttribute="trailing" id="4u6-YJ-00m"/>
                            <constraint firstItem="uBH-jh-cod" firstAttribute="trailing" secondItem="LR5-NX-OG0" secondAttribute="trailing" id="7d7-IE-5jy"/>
                            <constraint firstItem="m4r-LP-sGS" firstAttribute="leading" secondItem="LR5-NX-OG0" secondAttribute="leading" id="8hH-Q7-we8"/>
                            <constraint firstItem="uBH-jh-cod" firstAttribute="leading" secondItem="LR5-NX-OG0" secondAttribute="leading" id="BwR-bh-S08"/>
                            <constraint firstItem="Vtt-MU-A4x" firstAttribute="top" secondItem="bEV-so-nno" secondAttribute="top" constant="20" id="Cg8-Ph-B11"/>
                            <constraint firstItem="m4r-LP-sGS" firstAttribute="top" secondItem="LR5-NX-OG0" secondAttribute="top" id="Hlf-dL-Yuj"/>
                            <constraint firstItem="xgW-WZ-vbo" firstAttribute="centerY" secondItem="LR5-NX-OG0" secondAttribute="centerY" id="IGu-RW-N6R"/>
                            <constraint firstAttribute="bottom" secondItem="m4r-LP-sGS" secondAttribute="bottom" id="aDI-gf-IGF"/>
                            <constraint firstAttribute="bottom" secondItem="uBH-jh-cod" secondAttribute="bottom" id="gjq-mr-1cs"/>
                            <constraint firstItem="bEV-so-nno" firstAttribute="trailing" secondItem="Vtt-MU-A4x" secondAttribute="trailing" constant="10" id="jbc-rx-2gF"/>
                            <constraint firstItem="uBH-jh-cod" firstAttribute="top" secondItem="LR5-NX-OG0" secondAttribute="top" id="kNw-FR-fla"/>
                            <constraint firstItem="xgW-WZ-vbo" firstAttribute="centerX" secondItem="LR5-NX-OG0" secondAttribute="centerX" id="pxH-bg-Zip"/>
                            <constraint firstItem="Vtt-MU-A4x" firstAttribute="leading" secondItem="bEV-so-nno" secondAttribute="leading" constant="10" id="zEb-5y-dbr"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="cameraUnavailableLabel" destination="Vtt-MU-A4x" id="gge-K6-hho"/>
                        <outlet property="overlayView" destination="m4r-LP-sGS" id="qEI-SQ-5mM"/>
                        <outlet property="previewView" destination="uBH-jh-cod" id="0He-2J-Ihh"/>
                        <outlet property="resumeButton" destination="xgW-WZ-vbo" id="ahB-QA-JdH"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="nEJ-Gu-EMh" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-373.28244274809157" y="650.70422535211276"/>
        </scene>
        <!--Media Library View Controller-->
        <scene sceneID="Y0X-cv-ykk">
            <objects>
                <viewController storyboardIdentifier="MEDIA_LIBRARY_VIEW_CONTROLLER" id="NhF-T7-efv" customClass="MediaLibraryViewController" customModule="PoseLandmarker" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="zQB-07-hhL">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Click + to add an image or a video to begin running the pose detection." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="m31-ST-YG7">
                                <rect key="frame" x="10" y="419.33333333333331" width="373" height="38.333333333333314"/>
                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" translatesAutoresizingMaskIntoConstraints="NO" id="UT5-w7-OjM">
                                <rect key="frame" x="0.0" y="59" width="393" height="759"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sfh-5s-l7y" customClass="OverlayView" customModule="PoseLandmarker">
                                <rect key="frame" x="0.0" y="59" width="393" height="759"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="c8P-gU-0Bs">
                                <rect key="frame" x="0.0" y="59" width="393" height="759"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZFP-fC-3QA">
                                        <rect key="frame" x="0.0" y="340" width="393" height="79"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pose Detection in Progress" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="j6c-nz-Opy">
                                                <rect key="frame" x="0.0" y="34" width="393" height="19.333333333333329"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <progressView opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="750" progress="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="c9i-xv-hbd">
                                                <rect key="frame" x="121.66666666666669" y="10" width="150" height="4"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="4" id="bkg-Lv-yWZ"/>
                                                    <constraint firstAttribute="width" constant="150" id="kXJ-gS-96U"/>
                                                </constraints>
                                            </progressView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="c9i-xv-hbd" firstAttribute="top" secondItem="ZFP-fC-3QA" secondAttribute="top" constant="10" id="J2r-2v-2pK"/>
                                            <constraint firstItem="j6c-nz-Opy" firstAttribute="leading" secondItem="ZFP-fC-3QA" secondAttribute="leading" id="Qch-sS-Qba"/>
                                            <constraint firstAttribute="height" constant="79" id="gu2-Ze-o4W"/>
                                            <constraint firstItem="c9i-xv-hbd" firstAttribute="centerX" secondItem="ZFP-fC-3QA" secondAttribute="centerX" id="kI9-DV-WIk"/>
                                            <constraint firstItem="j6c-nz-Opy" firstAttribute="top" secondItem="c9i-xv-hbd" secondAttribute="bottom" constant="20" id="kyV-KW-9GD"/>
                                            <constraint firstAttribute="trailing" secondItem="j6c-nz-Opy" secondAttribute="trailing" id="vCX-tO-h16"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.99999600649999998" green="1" blue="1" alpha="0.64659648790000002" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="ZFP-fC-3QA" firstAttribute="leading" secondItem="c8P-gU-0Bs" secondAttribute="leading" id="KuN-BT-G14"/>
                                    <constraint firstItem="ZFP-fC-3QA" firstAttribute="centerY" secondItem="c8P-gU-0Bs" secondAttribute="centerY" id="NNf-tc-D9R"/>
                                    <constraint firstAttribute="trailing" secondItem="ZFP-fC-3QA" secondAttribute="trailing" id="wdf-Cf-08r"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4qW-7H-Z8p">
                                <rect key="frame" x="323" y="707" width="50" height="50"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="ic_add"/>
                                <connections>
                                    <action selector="onClickPickFromGallery:" destination="NhF-T7-efv" eventType="touchUpInside" id="g3Z-ih-3Vw"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="UIb-bm-QFk"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="m31-ST-YG7" firstAttribute="leading" secondItem="zQB-07-hhL" secondAttribute="leading" constant="10" id="0AJ-PC-d5j"/>
                            <constraint firstItem="UIb-bm-QFk" firstAttribute="trailing" secondItem="Sfh-5s-l7y" secondAttribute="trailing" id="0HM-3X-WU0"/>
                            <constraint firstItem="UIb-bm-QFk" firstAttribute="trailing" secondItem="4qW-7H-Z8p" secondAttribute="trailing" constant="20" id="3lk-SG-qzY"/>
                            <constraint firstItem="c8P-gU-0Bs" firstAttribute="top" secondItem="UIb-bm-QFk" secondAttribute="top" id="4Co-sF-TQp"/>
                            <constraint firstAttribute="trailing" secondItem="m31-ST-YG7" secondAttribute="trailing" constant="10" id="DQT-5f-XLI"/>
                            <constraint firstItem="Sfh-5s-l7y" firstAttribute="leading" secondItem="UIb-bm-QFk" secondAttribute="leading" id="IPV-gn-XFh"/>
                            <constraint firstItem="UIb-bm-QFk" firstAttribute="trailing" secondItem="UT5-w7-OjM" secondAttribute="trailing" id="Mrv-Fl-ldn"/>
                            <constraint firstItem="m31-ST-YG7" firstAttribute="centerY" secondItem="UIb-bm-QFk" secondAttribute="centerY" id="OT9-oc-NF0"/>
                            <constraint firstItem="UIb-bm-QFk" firstAttribute="bottom" secondItem="c8P-gU-0Bs" secondAttribute="bottom" id="RNu-UJ-PAh"/>
                            <constraint firstItem="Sfh-5s-l7y" firstAttribute="top" secondItem="UIb-bm-QFk" secondAttribute="top" id="T7u-wF-6n9"/>
                            <constraint firstItem="UIb-bm-QFk" firstAttribute="trailing" secondItem="c8P-gU-0Bs" secondAttribute="trailing" id="Vkv-zN-6wy"/>
                            <constraint firstItem="UT5-w7-OjM" firstAttribute="top" secondItem="UIb-bm-QFk" secondAttribute="top" id="bVG-eg-kOs"/>
                            <constraint firstItem="UIb-bm-QFk" firstAttribute="bottom" secondItem="UT5-w7-OjM" secondAttribute="bottom" id="gHb-cL-IOX"/>
                            <constraint firstItem="UIb-bm-QFk" firstAttribute="bottom" secondItem="Sfh-5s-l7y" secondAttribute="bottom" id="jI9-NA-KQR"/>
                            <constraint firstItem="c8P-gU-0Bs" firstAttribute="leading" secondItem="UIb-bm-QFk" secondAttribute="leading" id="jO0-g2-yTY"/>
                            <constraint firstItem="UIb-bm-QFk" firstAttribute="bottom" secondItem="4qW-7H-Z8p" secondAttribute="bottom" constant="61" id="pf9-L7-SgR"/>
                            <constraint firstItem="UT5-w7-OjM" firstAttribute="leading" secondItem="UIb-bm-QFk" secondAttribute="leading" id="q8g-cY-pZ4"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="imageEmptyLabel" destination="m31-ST-YG7" id="WDx-UD-6Do"/>
                        <outlet property="overlayView" destination="Sfh-5s-l7y" id="kXw-q0-wZw"/>
                        <outlet property="pickFromGalleryButton" destination="4qW-7H-Z8p" id="93a-aa-nv7"/>
                        <outlet property="pickFromGalleryButtonBottomSpace" destination="pf9-L7-SgR" id="s14-EG-MiX"/>
                        <outlet property="pickedImageView" destination="UT5-w7-OjM" id="g7o-Vr-iSG"/>
                        <outlet property="progressView" destination="c9i-xv-hbd" id="IDY-jo-jaz"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="X2F-JF-bls" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="281.67938931297709" y="687.32394366197184"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_add" width="50" height="50"/>
        <image name="ic_camera" width="33.333332061767578" height="33.333332061767578"/>
        <image name="ic_expand_down" width="50" height="50"/>
        <image name="ic_expand_up" width="50" height="50"/>
        <image name="ic_library" width="33.333332061767578" height="33.333332061767578"/>
        <image name="ic_mediapipe" width="216" height="216"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
