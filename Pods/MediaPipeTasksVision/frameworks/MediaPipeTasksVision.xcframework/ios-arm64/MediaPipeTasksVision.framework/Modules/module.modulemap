framework module MediaPipeTasksVision {
  umbrella header "MediaPipeTasksVision.h"
  export *
  module * { export * }
  link "dl"
  link "m"
  link "pthread"
  link framework "AVFoundation"
  link framework "Accelerate"
  link framework "AssetsLibrary"
  link framework "CoreFoundation"
  link framework "CoreGraphics"
  link framework "CoreImage"
  link framework "CoreMedia"
  link framework "CoreVideo"
  link framework "GLKit"
  link framework "Metal"
  link framework "MetalKit"
  link framework "OpenGLES"
  link framework "QuartzCore"
  link framework "UIKit"
}
