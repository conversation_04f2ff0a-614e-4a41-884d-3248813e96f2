#import <MediaPipeTasksVision/MPPBaseOptions.h>
#import <MediaPipeTasksVision/MPPCategory.h>
#import <MediaPipeTasksVision/MPPClassificationResult.h>
#import <MediaPipeTasksVision/MPPClassifierOptions.h>
#import <MediaPipeTasksVision/MPPCommon.h>
#import <MediaPipeTasksVision/MPPConnection.h>
#import <MediaPipeTasksVision/MPPDetection.h>
#import <MediaPipeTasksVision/MPPEmbedding.h>
#import <MediaPipeTasksVision/MPPEmbeddingResult.h>
#import <MediaPipeTasksVision/MPPFaceDetector.h>
#import <MediaPipeTasksVision/MPPFaceDetectorOptions.h>
#import <MediaPipeTasksVision/MPPFaceDetectorResult.h>
#import <MediaPipeTasksVision/MPPFaceLandmarker.h>
#import <MediaPipeTasksVision/MPPFaceLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPFaceLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPFaceStylizer.h>
#import <MediaPipeTasksVision/MPPFaceStylizerOptions.h>
#import <MediaPipeTasksVision/MPPFaceStylizerResult.h>
#import <MediaPipeTasksVision/MPPGestureRecognizer.h>
#import <MediaPipeTasksVision/MPPGestureRecognizerOptions.h>
#import <MediaPipeTasksVision/MPPGestureRecognizerResult.h>
#import <MediaPipeTasksVision/MPPHandLandmarker.h>
#import <MediaPipeTasksVision/MPPHandLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPHandLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPImage.h>
#import <MediaPipeTasksVision/MPPImageClassifier.h>
#import <MediaPipeTasksVision/MPPImageClassifierOptions.h>
#import <MediaPipeTasksVision/MPPImageClassifierResult.h>
#import <MediaPipeTasksVision/MPPImageEmbedder.h>
#import <MediaPipeTasksVision/MPPImageEmbedderOptions.h>
#import <MediaPipeTasksVision/MPPImageEmbedderResult.h>
#import <MediaPipeTasksVision/MPPImageSegmenter.h>
#import <MediaPipeTasksVision/MPPImageSegmenterOptions.h>
#import <MediaPipeTasksVision/MPPImageSegmenterResult.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenter.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenterOptions.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenterResult.h>
#import <MediaPipeTasksVision/MPPLandmark.h>
#import <MediaPipeTasksVision/MPPMask.h>
#import <MediaPipeTasksVision/MPPObjectDetector.h>
#import <MediaPipeTasksVision/MPPObjectDetectorOptions.h>
#import <MediaPipeTasksVision/MPPObjectDetectorResult.h>
#import <MediaPipeTasksVision/MPPPoseLandmarker.h>
#import <MediaPipeTasksVision/MPPPoseLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPPoseLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPRegionOfInterest.h>
#import <MediaPipeTasksVision/MPPRunningMode.h>
#import <MediaPipeTasksVision/MPPTaskOptions.h>
#import <MediaPipeTasksVision/MPPTaskResult.h>
#import <MediaPipeTasksVision/MPPBaseOptions.h>
#import <MediaPipeTasksVision/MPPCategory.h>
#import <MediaPipeTasksVision/MPPClassificationResult.h>
#import <MediaPipeTasksVision/MPPClassifierOptions.h>
#import <MediaPipeTasksVision/MPPCommon.h>
#import <MediaPipeTasksVision/MPPConnection.h>
#import <MediaPipeTasksVision/MPPDetection.h>
#import <MediaPipeTasksVision/MPPEmbedding.h>
#import <MediaPipeTasksVision/MPPEmbeddingResult.h>
#import <MediaPipeTasksVision/MPPFaceDetector.h>
#import <MediaPipeTasksVision/MPPFaceDetectorOptions.h>
#import <MediaPipeTasksVision/MPPFaceDetectorResult.h>
#import <MediaPipeTasksVision/MPPFaceLandmarker.h>
#import <MediaPipeTasksVision/MPPFaceLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPFaceLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPFaceStylizer.h>
#import <MediaPipeTasksVision/MPPFaceStylizerOptions.h>
#import <MediaPipeTasksVision/MPPFaceStylizerResult.h>
#import <MediaPipeTasksVision/MPPGestureRecognizer.h>
#import <MediaPipeTasksVision/MPPGestureRecognizerOptions.h>
#import <MediaPipeTasksVision/MPPGestureRecognizerResult.h>
#import <MediaPipeTasksVision/MPPHandLandmarker.h>
#import <MediaPipeTasksVision/MPPHandLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPHandLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPImage.h>
#import <MediaPipeTasksVision/MPPImageClassifier.h>
#import <MediaPipeTasksVision/MPPImageClassifierOptions.h>
#import <MediaPipeTasksVision/MPPImageClassifierResult.h>
#import <MediaPipeTasksVision/MPPImageEmbedder.h>
#import <MediaPipeTasksVision/MPPImageEmbedderOptions.h>
#import <MediaPipeTasksVision/MPPImageEmbedderResult.h>
#import <MediaPipeTasksVision/MPPImageSegmenter.h>
#import <MediaPipeTasksVision/MPPImageSegmenterOptions.h>
#import <MediaPipeTasksVision/MPPImageSegmenterResult.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenter.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenterOptions.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenterResult.h>
#import <MediaPipeTasksVision/MPPLandmark.h>
#import <MediaPipeTasksVision/MPPMask.h>
#import <MediaPipeTasksVision/MPPObjectDetector.h>
#import <MediaPipeTasksVision/MPPObjectDetectorOptions.h>
#import <MediaPipeTasksVision/MPPObjectDetectorResult.h>
#import <MediaPipeTasksVision/MPPPoseLandmarker.h>
#import <MediaPipeTasksVision/MPPPoseLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPPoseLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPRegionOfInterest.h>
#import <MediaPipeTasksVision/MPPRunningMode.h>
#import <MediaPipeTasksVision/MPPTaskOptions.h>
#import <MediaPipeTasksVision/MPPTaskResult.h>
#import <MediaPipeTasksVision/MPPBaseOptions.h>
#import <MediaPipeTasksVision/MPPCategory.h>
#import <MediaPipeTasksVision/MPPClassificationResult.h>
#import <MediaPipeTasksVision/MPPClassifierOptions.h>
#import <MediaPipeTasksVision/MPPCommon.h>
#import <MediaPipeTasksVision/MPPConnection.h>
#import <MediaPipeTasksVision/MPPDetection.h>
#import <MediaPipeTasksVision/MPPEmbedding.h>
#import <MediaPipeTasksVision/MPPEmbeddingResult.h>
#import <MediaPipeTasksVision/MPPFaceDetector.h>
#import <MediaPipeTasksVision/MPPFaceDetectorOptions.h>
#import <MediaPipeTasksVision/MPPFaceDetectorResult.h>
#import <MediaPipeTasksVision/MPPFaceLandmarker.h>
#import <MediaPipeTasksVision/MPPFaceLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPFaceLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPFaceStylizer.h>
#import <MediaPipeTasksVision/MPPFaceStylizerOptions.h>
#import <MediaPipeTasksVision/MPPFaceStylizerResult.h>
#import <MediaPipeTasksVision/MPPGestureRecognizer.h>
#import <MediaPipeTasksVision/MPPGestureRecognizerOptions.h>
#import <MediaPipeTasksVision/MPPGestureRecognizerResult.h>
#import <MediaPipeTasksVision/MPPHandLandmarker.h>
#import <MediaPipeTasksVision/MPPHandLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPHandLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPImage.h>
#import <MediaPipeTasksVision/MPPImageClassifier.h>
#import <MediaPipeTasksVision/MPPImageClassifierOptions.h>
#import <MediaPipeTasksVision/MPPImageClassifierResult.h>
#import <MediaPipeTasksVision/MPPImageEmbedder.h>
#import <MediaPipeTasksVision/MPPImageEmbedderOptions.h>
#import <MediaPipeTasksVision/MPPImageEmbedderResult.h>
#import <MediaPipeTasksVision/MPPImageSegmenter.h>
#import <MediaPipeTasksVision/MPPImageSegmenterOptions.h>
#import <MediaPipeTasksVision/MPPImageSegmenterResult.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenter.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenterOptions.h>
#import <MediaPipeTasksVision/MPPInteractiveSegmenterResult.h>
#import <MediaPipeTasksVision/MPPLandmark.h>
#import <MediaPipeTasksVision/MPPMask.h>
#import <MediaPipeTasksVision/MPPObjectDetector.h>
#import <MediaPipeTasksVision/MPPObjectDetectorOptions.h>
#import <MediaPipeTasksVision/MPPObjectDetectorResult.h>
#import <MediaPipeTasksVision/MPPPoseLandmarker.h>
#import <MediaPipeTasksVision/MPPPoseLandmarkerOptions.h>
#import <MediaPipeTasksVision/MPPPoseLandmarkerResult.h>
#import <MediaPipeTasksVision/MPPRegionOfInterest.h>
#import <MediaPipeTasksVision/MPPRunningMode.h>
#import <MediaPipeTasksVision/MPPTaskOptions.h>
#import <MediaPipeTasksVision/MPPTaskResult.h>
