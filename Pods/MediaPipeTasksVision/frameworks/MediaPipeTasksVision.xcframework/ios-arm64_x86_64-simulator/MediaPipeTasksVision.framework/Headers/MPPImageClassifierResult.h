// Copyright 2023 The MediaPipe Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#import <Foundation/Foundation.h>
#import "MPPClassificationResult.h"
#import "MPPTaskResult.h"

NS_ASSUME_NONNULL_BEGIN

/** Represents the classification results generated by `ImageClassifier`. **/
NS_SWIFT_NAME(ImageClassifierResult)
@interface MPPImageClassifierResult : MPPTaskResult

/** The `ClassificationResult` instance containing one set of results per classifier head. **/
@property(nonatomic, readonly) MPPClassificationResult *classificationResult;

/**
 * Initializes a new `ImageClassifierResult` with the given `ClassificationResult` and
 * timestamp (in milliseconds).
 *
 * @param classificationResult The `ClassificationResult` instance containing one set of results
 * per classifier head.
 * @param timestampInMilliseconds The timestamp (in milliseconds) for this result.
 *
 * @return An instance of `ImageClassifierResult` initialized with the given
 * `ClassificationResult` and timestamp (in milliseconds).
 */
- (instancetype)initWithClassificationResult:(MPPClassificationResult *)classificationResult
                     timestampInMilliseconds:(NSInteger)timestampInMilliseconds;

@end

NS_ASSUME_NONNULL_END
