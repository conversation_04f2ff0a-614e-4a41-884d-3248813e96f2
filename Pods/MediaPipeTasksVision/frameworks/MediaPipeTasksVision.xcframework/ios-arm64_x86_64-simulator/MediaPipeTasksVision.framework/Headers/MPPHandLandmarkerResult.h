// Copyright 2023 The MediaPipe Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#import <Foundation/Foundation.h>

#import "MPPCategory.h"
#import "MPPLandmark.h"
#import "MPPTaskResult.h"

NS_ASSUME_NONNULL_BEGIN

/** Represents the hand landmarker results generated by `HandLandmarker`. */
NS_SWIFT_NAME(HandLandmarkerResult)
@interface MPPHandLandmarkerResult : MPPTaskResult

/** Hand landmarks of detected hands. */
@property(nonatomic, readonly) NSArray<NSArray<MPPNormalizedLandmark *> *> *landmarks;

/** Hand landmarks in world coordinates of detected hands. */
@property(nonatomic, readonly) NSArray<NSArray<MPPLandmark *> *> *worldLandmarks;

/** Handedness of detected hands. */
@property(nonatomic, readonly) NSArray<NSArray<MPPCategory *> *> *handedness;

/**
 * Initializes a new `HandLandmarkerResult` with the given landmarks, world landmarks, handedness
 * and timestamp (in milliseconds).
 *
 * @param landmarks The hand landmarks of detected hands.
 * @param worldLandmarks The hand landmarks in world coordinates of detected hands.
 * @param handedness The handedness of detected hands.
 * @param timestampInMilliseconds The timestamp for this result.
 *
 * @return An instance of `HandLandmarkerResult` initialized with the given landmarks, world
 * landmarks, handedness and timestamp (in milliseconds).
 *
 */
- (instancetype)initWithLandmarks:(NSArray<NSArray<MPPNormalizedLandmark *> *> *)landmarks
                   worldLandmarks:(NSArray<NSArray<MPPLandmark *> *> *)worldLandmarks
                       handedness:(NSArray<NSArray<MPPCategory *> *> *)handedness
          timestampInMilliseconds:(NSInteger)timestampInMilliseconds;

@end

NS_ASSUME_NONNULL_END
