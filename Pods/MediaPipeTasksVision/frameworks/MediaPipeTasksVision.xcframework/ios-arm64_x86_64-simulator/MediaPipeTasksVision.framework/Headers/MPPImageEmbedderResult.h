// Copyright 2023 The MediaPipe Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#import <Foundation/Foundation.h>
#import "MPPEmbeddingResult.h"
#import "MPPTaskResult.h"

NS_ASSUME_NONNULL_BEGIN

/** Represents the embedding results generated by `ImageEmbedder`. **/
NS_SWIFT_NAME(ImageEmbedderResult)
@interface MPPImageEmbedderResult : MPPTaskResult

/** The `MPPEmbedderResult` instance containing one embedding per embedder head. **/
@property(nonatomic, readonly) MPPEmbeddingResult *embeddingResult;

/**
 * Initializes a new `ImageEmbedderResult` with the given `MPPEmbeddingResult` and
 * timestamp (in milliseconds).
 *
 * @param embeddingResult The `EmbeddingResult` instance containing one set of results per
 * classifier head.
 * @param timestampInMilliseconds The timestamp (in millisecondss) for this result.
 *
 * @return An instance of `ImageEmbedderResult` initialized with the given
 * `MPPEmbeddingResult` and timestamp (in milliseconds).
 */
- (instancetype)initWithEmbeddingResult:(nullable MPPEmbeddingResult *)embeddingResult
                timestampInMilliseconds:(NSInteger)timestampInMilliseconds;

- (instancetype)init NS_UNAVAILABLE;

+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END
