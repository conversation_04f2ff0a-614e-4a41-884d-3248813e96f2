// Copyright 2023 The MediaPipe Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#import <Foundation/Foundation.h>
#import "MPPClassificationResult.h"
#import "MPPLandmark.h"
#import "MPPTaskResult.h"

NS_ASSUME_NONNULL_BEGIN

/** A matrix that can be used for tansformations. */
NS_SWIFT_NAME(TransformMatrix)
@interface MPPTransformMatrix : NSObject
/** The number of rows. */
@property(nonatomic, readonly) NSUInteger rows;

/** The number of columns. */
@property(nonatomic, readonly) NSUInteger columns;

/** The values of the transform matrix. */
@property(nonatomic, readonly) float *data;

/**
 * Creates a new MPPTransformMatrix.
 *
 * @param data Pointer to the memory location where the data is stored. The data is copied.
 * @param rows The number of rows.
 * @param columns The number of columns.
 */
- (instancetype)initWithData:(const float *)data
                        rows:(NSInteger)rows
                     columns:(NSInteger)columns NS_DESIGNATED_INITIALIZER;

- (instancetype)init NS_UNAVAILABLE;

/**
 * Returns the value located at the specified location. An NSRangeException is raised if the
 * location is outside the range of the matrix.
 */
- (float)valueAtRow:(NSUInteger)row column:(NSUInteger)column;

+ (instancetype)new NS_UNAVAILABLE;

@end

/** Represents the detection results generated by `FaceLandmarker`. */
NS_SWIFT_NAME(FaceLandmarkerResult)
@interface MPPFaceLandmarkerResult : MPPTaskResult

/** Detected face landmarks in normalized image coordinates. */
@property(nonatomic, readonly) NSArray<NSArray<MPPNormalizedLandmark *> *> *faceLandmarks;

/**
 * Face blendshapes results. Defaults to an empty array if not enabled.
 */
@property(nonatomic, readonly) NSArray<MPPClassifications *> *faceBlendshapes;

/**
 * Facial transformation 4x4 matrices. Defaults to an empty array if not enabled.
 */
@property(nonatomic, readonly) NSArray<MPPTransformMatrix *> *facialTransformationMatrixes;

/**
 * Initializes a new `FaceLandmarkerResult` with the given array of landmarks, blendshapes,
 * facialTransformationMatrixes and timestamp (in milliseconds).
 *
 * @param faceLandmarks An array of `NormalizedLandmark` objects.
 * @param faceBlendshapes An array of `Classifications` objects.
 * @param facialTransformationMatrixes An array of flattended matrices.
 * @param timestampInMilliseconds The timestamp (in milliseconds) for this result.
 *
 * @return An instance of `FaceLandmarkerResult` initialized with the given array of detections and
 * timestamp (in milliseconds).
 */
- (instancetype)initWithFaceLandmarks:(NSArray<NSArray<MPPNormalizedLandmark *> *> *)faceLandmarks
                      faceBlendshapes:(NSArray<MPPClassifications *> *)faceBlendshapes
         facialTransformationMatrixes:(NSArray<MPPTransformMatrix *> *)facialTransformationMatrixes
              timestampInMilliseconds:(NSInteger)timestampInMilliseconds NS_DESIGNATED_INITIALIZER;

- (instancetype)initWithTimestampInMilliseconds:(NSInteger)timestampInMilliseconds NS_UNAVAILABLE;

- (instancetype)init NS_UNAVAILABLE;

+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END
