// Copyright 2023 The MediaPipe Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#import <Foundation/Foundation.h>
#import "MPPLandmark.h"
#import "MPPTaskResult.h"
#import "MPPMask.h"

NS_ASSUME_NONNULL_BEGIN

/** Represents the pose landmarks deection results generated by `PoseLandmarker`. */
NS_SWIFT_NAME(PoseLandmarkerResult)
@interface MPPPoseLandmarkerResult : MPPTaskResult

/** Pose landmarks of detected poses. */
@property(nonatomic, readonly) NSArray<NSArray<MPPNormalizedLandmark *> *> *landmarks;

/** Pose landmarks in world coordinates of detected poses. */
@property(nonatomic, readonly) NSArray<NSArray<MPPLandmark *> *> *worldLandmarks;

/** Pose segmentation masks. */
@property(nonatomic, readonly) NSArray<MPPMask *> *segmentationMasks;

/**
 * Initializes a new `PoseLandmarkerResult` with the given array of landmarks, world landmarks,
 * segmentation masks of the detected poses and timestamp (in milliseconds).
 *
 * @param landmarks An array of `NormalizedLandmark` objects.
 * @param worldLandmarks An array of `Landmark` objects.
 * @param segmentationMasks An array of `Mask` objects.
 * @param timestampInMilliseconds The timestamp (in milliseconds) for this result.
 *
 * @return An instance of `PoseLandmarkerResult` initialized with the given array of landmarks,
 * world landmarks, segmentation masks of the detected poses and timestamp (in milliseconds).
 */
- (instancetype)initWithLandmarks:(NSArray<NSArray<MPPNormalizedLandmark *> *> *)landmarks
                   worldLandmarks:(NSArray<NSArray<MPPLandmark *> *> *)worldLandmarks
                segmentationMasks:(nullable NSArray<MPPMask *> *)segmentationMasks
          timestampInMilliseconds:(NSInteger)timestampInMilliseconds NS_DESIGNATED_INITIALIZER;

- (instancetype)initWithTimestampInMilliseconds:(NSInteger)timestampInMilliseconds NS_UNAVAILABLE;

- (instancetype)init NS_UNAVAILABLE;

+ (instancetype)new NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END
